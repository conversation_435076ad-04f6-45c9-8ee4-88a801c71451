
import ServicePageTemplate from "@/components/ServicePageTemplate";

const SupportMaintenance = () => {
  const features = [
    "30-minute response time guarantee",
    "24/7 monitoring and support coverage",
    "Proactive maintenance and updates",
    "Priority escalation for critical issues",
    "Detailed reporting and documentation",
    "Preventive maintenance scheduling"
  ];

  const benefits = [
    "Minimal downtime with fast response times",
    "Expert support from certified technicians",
    "Proactive approach prevents major issues",
    "Transparent communication and reporting",
    "Cost-effective compared to in-house teams"
  ];

  return (
    <ServicePageTemplate
      title="Support & Maintenance"
      subtitle="Expert help when you need it, with quick response times that keep you moving"
      description="Keep your systems running smoothly with our comprehensive support and maintenance services. We provide rapid response times and proactive monitoring to prevent issues before they impact your business."
      icon="🛠️"
      commitment="30-min response"
      features={features}
      benefits={benefits}
    />
  );
};

export default SupportMaintenance;
