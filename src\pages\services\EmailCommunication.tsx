
import ServicePageTemplate from "@/components/ServicePageTemplate";

const EmailCommunication = () => {
  const features = [
    "99.99% inbox delivery rate guaranteed",
    "Professional email hosting with your domain",
    "Advanced spam filtering and security",
    "Mobile-friendly email templates",
    "Analytics and delivery reporting",
    "Integration with existing business tools"
  ];

  const benefits = [
    "Reliable communication that reaches customers",
    "Professional image with custom domain emails",
    "Reduced IT overhead with managed solutions",
    "Improved deliverability over generic providers",
    "Dedicated support from email experts"
  ];

  return (
    <ServicePageTemplate
      title="Email & Communication"
      subtitle="Email systems that actually reach your customers' inboxes, every time"
      description="Stop worrying about whether your emails are reaching customers. Our email systems ensure 99.99% inbox delivery with professional hosting and advanced security."
      icon="📧"
      commitment="99.99% inbox delivery"
      features={features}
      benefits={benefits}
    />
  );
};

export default EmailCommunication;
