
import ServicePageTemplate from "@/components/ServicePageTemplate";

const SecurityCompliance = () => {
  const features = [
    "24/7 security monitoring and threat detection",
    "Critical security fixes within 72 hours",
    "Compliance with industry standards (GDPR, HIPAA, etc.)",
    "Regular security audits and penetration testing",
    "Employee security training programs",
    "Incident response planning and execution"
  ];

  const benefits = [
    "Peace of mind with enterprise-grade security",
    "Compliance with regulations and standards",
    "Rapid response to emerging threats",
    "Protection of customer and business data",
    "Reduced risk of costly security breaches"
  ];

  return (
    <ServicePageTemplate
      title="Security & Compliance"
      subtitle="Protection against threats with rapid response when issues arise"
      description="Protect your business and customer data with our comprehensive security solutions. We provide 24/7 monitoring, rapid threat response, and compliance with industry standards."
      icon="🔒"
      commitment="Critical fixes ≤ 72h"
      features={features}
      benefits={benefits}
    />
  );
};

export default SecurityCompliance;
