
import ServicePageTemplate from "@/components/ServicePageTemplate";

const WebDigital = () => {
  const features = [
    "Lightning-fast page load times under 1 second",
    "99.9% uptime guarantee with monitoring",
    "Mobile-responsive design that works everywhere",
    "SEO optimization for better search rankings",
    "Modern, clean design that converts visitors",
    "Secure hosting with automatic backups"
  ];

  const benefits = [
    "Proven track record with 500+ successful projects",
    "Custom solutions tailored to your business needs",
    "Ongoing support and maintenance included",
    "Performance optimization that increases conversions",
    "Future-proof technology stack"
  ];

  return (
    <ServicePageTemplate
      title="Web & Digital"
      subtitle="Fast-loading websites and web apps that stay online when your customers need them"
      description="We build digital platforms that perform. Our websites load in under 1 second and maintain 99.9% uptime, ensuring your customers always have access to your business."
      icon="🌐"
      commitment="Pages < 1s, 99.9% uptime"
      features={features}
      benefits={benefits}
    />
  );
};

export default WebDigital;
