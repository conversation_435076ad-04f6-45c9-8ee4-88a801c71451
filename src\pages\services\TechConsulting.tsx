
import ServicePageTemplate from "@/components/ServicePageTemplate";

const TechConsulting = () => {
  const features = [
    "Comprehensive technology assessment",
    "30-day strategic roadmap delivery",
    "Budget planning and cost optimization",
    "Technology stack recommendations",
    "Digital transformation planning",
    "Ongoing strategic advisory services"
  ];

  const benefits = [
    "Clear technology strategy aligned with business goals",
    "Reduced technology costs and improved efficiency",
    "Expert guidance from experienced consultants",
    "Faster decision-making with data-driven insights",
    "Competitive advantage through smart technology choices"
  ];

  return (
    <ServicePageTemplate
      title="Tech Consulting"
      subtitle="Clear technology strategy that aligns with your business goals"
      description="Get expert guidance on your technology decisions. Our consultants help you build a strategic roadmap that maximizes ROI and positions your business for growth."
      icon="🎯"
      commitment="30-day roadmap"
      features={features}
      benefits={benefits}
    />
  );
};

export default TechConsulting;
