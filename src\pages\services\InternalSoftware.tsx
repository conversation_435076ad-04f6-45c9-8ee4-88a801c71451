
import ServicePageTemplate from "@/components/ServicePageTemplate";

const InternalSoftware = () => {
  const features = [
    "Custom automation tools for repetitive tasks",
    "Integration with existing business systems",
    "User-friendly interfaces your team will love",
    "Scalable solutions that grow with you",
    "Training and documentation included",
    "Ongoing support and feature updates"
  ];

  const benefits = [
    "Minimum 30% reduction in manual tasks",
    "Improved accuracy and reduced human error",
    "Better data insights and reporting",
    "Streamlined workflows across departments",
    "ROI typically achieved within 6 months"
  ];

  return (
    <ServicePageTemplate
      title="Internal Software & Automation"
      subtitle="Custom tools that eliminate repetitive work and streamline your operations"
      description="Transform your business processes with custom software solutions. We build internal tools that automate workflows, integrate systems, and give your team more time for high-value work."
      icon="⚡"
      commitment="Tasks cut ≥ 30%"
      features={features}
      benefits={benefits}
    />
  );
};

export default InternalSoftware;
