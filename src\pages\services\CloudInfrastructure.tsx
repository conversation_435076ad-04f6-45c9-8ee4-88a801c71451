
import ServicePageTemplate from "@/components/ServicePageTemplate";

const CloudInfrastructure = () => {
  const features = [
    "Auto-scaling servers that grow with your business",
    "Automated backups every 15 minutes",
    "99.99% uptime with redundant systems",
    "Global CDN for worldwide performance",
    "24/7 monitoring and alerting",
    "Disaster recovery planning included"
  ];

  const benefits = [
    "Scalable infrastructure that adapts to demand",
    "Cost-effective solutions with transparent pricing",
    "Enterprise-grade security and compliance",
    "Expert management so you can focus on business",
    "Proven reliability with Fortune 500 companies"
  ];

  return (
    <ServicePageTemplate
      title="Cloud Infrastructure"
      subtitle="Servers that grow with your business and protect your data automatically"
      description="Our cloud infrastructure solutions ensure your applications stay online and perform optimally, no matter how fast you grow or how much traffic you receive."
      icon="☁️"
      commitment="Autoscale < 40s, backups every 15 min"
      features={features}
      benefits={benefits}
    />
  );
};

export default CloudInfrastructure;
